{"class_name": "Countersink hole", "rules": [{"id": "CS_001", "category": "geometry_validation", "title": "<PERSON><PERSON>", "description": "Metric screws require 90° countersink angle", "rule": "For metric screws, countersink angle must be 90°", "severity": "error", "validation_code": "screw_type == 'metric' && countersink_angle == 90", "error_message": "ERROR: Metric screws require 90° countersink angle.", "parameters": ["screw_type", "countersink_angle"]}, {"id": "CS_002", "category": "geometry_validation", "title": "Sheet Metal Screw Countersink Angle", "description": "Sheet metal screws require 82° or 100° countersink angle", "rule": "For sheet metal screws, countersink angle must be 82° or 100°", "severity": "error", "validation_code": "screw_type == 'sheet_metal' && (countersink_angle == 82 || countersink_angle == 100)", "error_message": "ERROR: Sheet metal screws require 82° or 100° countersink angle.", "parameters": ["screw_type", "countersink_angle"]}, {"id": "CS_003", "category": "manufacturing_constraint", "title": "Minimum Sheet Thickness", "description": "Sheet thickness must be sufficient for countersink depth", "rule": "Sheet thickness ≥ countersink depth + 0.5 mm", "severity": "error", "validation_code": "sheet_thickness >= (countersink_depth + 0.5)", "error_message": "ERROR: Sheet thickness insufficient. Minimum thickness = countersink depth + 0.5mm.", "parameters": ["sheet_thickness", "countersink_depth"]}, {"id": "CS_004", "category": "geometry_validation", "title": "Countersink Diameter to <PERSON><PERSON>", "description": "Countersink diameter must be adapted to screw size", "rule": "Countersink diameter must match screw size specifications (e.g., M6 → ⌀11mm)", "severity": "warning", "validation_code": "countersink_diameter == expected_diameter_for_screw_size", "error_message": "WARNING: Countersink diameter may not match screw size. Verify dimensions.", "parameters": ["screw_size", "countersink_diameter"]}, {"id": "CS_005", "category": "manufacturing_constraint", "title": "Thin Wall Countersinking", "description": "Avoid countersinking on walls thinner than 2mm without reinforcement", "rule": "Wall thickness ≥ 2mm for countersinking without reinforcement", "severity": "warning", "validation_code": "wall_thickness >= 2.0 || has_reinforcement == true", "error_message": "WARNING: Wall too thin for countersinking. Consider reinforcement or alternative fastening.", "parameters": ["wall_thickness", "has_reinforcement"]}, {"id": "CS_006", "category": "geometry_validation", "title": "Through vs Blind Countersink Specification", "description": "Countersink type must be clearly specified", "rule": "Countersink must be specified as either through-hole or blind", "severity": "error", "validation_code": "countersink_type == 'through' || countersink_type == 'blind'", "error_message": "ERROR: Countersink type must be specified (through or blind).", "parameters": ["countersink_type"]}, {"id": "CS_013", "category": "auto_determination", "title": "Auto-determine <PERSON>rew <PERSON> from Angle", "description": "Automatically determine screw type based on countersink angle", "rule": "If angle is 90°, use metric screw; if 82° or 100°, use sheet metal screw", "severity": "info", "validation_code": "(countersink_angle == 90 && screw_type == 'metric') || ((countersink_angle == 82 || countersink_angle == 100) && screw_type == 'sheet_metal')", "error_message": "INFO: <PERSON>rew type auto-determined from countersink angle.", "parameters": ["countersink_angle"], "priority": "high", "auto_determine": {"when": "screw_type is missing or angle changes", "logic": "if countersink_angle == 90 then screw_type = 'metric'; if countersink_angle == 82 or 100 then screw_type = 'sheet_metal'"}}, {"id": "CS_014", "category": "auto_determination", "title": "Auto-determine and Override Standard Dimensions", "description": "Automatically determine and override d1, d2, countersink_depth from thread_size", "rule": "Use standard lookup table to determine and apply dimensions from thread size", "severity": "info", "validation_code": "true", "error_message": "INFO: Standard dimensions auto-applied from thread size.", "parameters": ["thread_size"], "priority": "highest", "auto_determine": {"when": "thread_size is provided", "action": "override_existing_values", "logic": {"M4_metric": {"d1": 4.5, "d2": 8.6, "countersink_depth": 2.1, "screw_type": "metric"}, "M5_metric": {"d1": 5.5, "d2": 10.4, "countersink_depth": 2.5, "screw_type": "metric"}, "M6_metric": {"d1": 6.6, "d2": 12.4, "countersink_depth": 2.9, "screw_type": "metric"}, "M8_metric": {"d1": 9, "d2": 16.4, "countersink_depth": 3.7, "screw_type": "metric"}, "M10_metric": {"d1": 11, "d2": 20.4, "countersink_depth": 4.7, "screw_type": "metric"}, "M12_metric": {"d1": 13.5, "d2": 24.4, "countersink_depth": 5.2, "screw_type": "metric"}, "M14_metric": {"d1": 15.5, "d2": 27.4, "countersink_depth": 5.7, "screw_type": "metric"}, "M16_metric": {"d1": 17.5, "d2": 32.4, "countersink_depth": 7.2, "screw_type": "metric"}, "M18_metric": {"d1": 20, "d2": 36.4, "countersink_depth": 8.2, "screw_type": "metric"}, "M20_metric": {"d1": 22, "d2": 40.4, "countersink_depth": 9.2, "screw_type": "metric"}, "M4_sheet_metal": {"d1": 4.5, "d2": 8.6, "countersink_depth": 2.1, "screw_type": "sheet_metal"}, "M5_sheet_metal": {"d1": 5.5, "d2": 10.4, "countersink_depth": 2.5, "screw_type": "sheet_metal"}, "M6_sheet_metal": {"d1": 6.6, "d2": 12.4, "countersink_depth": 2.9, "screw_type": "sheet_metal"}, "M8_sheet_metal": {"d1": 9, "d2": 16.4, "countersink_depth": 3.7, "screw_type": "sheet_metal"}, "M10_sheet_metal": {"d1": 11, "d2": 20.4, "countersink_depth": 4.7, "screw_type": "sheet_metal"}, "M12_sheet_metal": {"d1": 13.5, "d2": 24.4, "countersink_depth": 5.2, "screw_type": "sheet_metal"}}}}, {"id": "CS_015", "category": "auto_correction", "title": "Auto-correct Through Hole Depth", "description": "Automatically set hole depth to full thickness for through holes", "rule": "For through holes, hole depth should equal material thickness", "severity": "info", "validation_code": "hole_type != 'through' || hole_depth == material_thickness", "error_message": "INFO: Through hole depth auto-corrected to material thickness.", "parameters": ["hole_type", "material_thickness"], "priority": "high", "auto_determine": {"when": "hole_type == 'through'", "action": "set_hole_depth_to_material_thickness", "logic": "hole_depth = material_thickness"}}, {"id": "CS_021", "category": "workflow_enforcement", "title": "<PERSON><PERSON><PERSON> Must Be First - Block All Other Questions", "description": "When thread size is missing for countersink hole, ONLY ask for thread size and block all other questions", "rule": "For countersink holes, thread size is mandatory first parameter - no other questions allowed until provided", "severity": "error", "validation_code": "shape_class == 'Countersink hole' && (thread_size == null || thread_size == '')", "error_message": "RULE VIOLATION WARNING: Thread size must be specified first for countersink holes.", "parameters": ["thread_size"], "priority": "critical", "workflow_step": 0, "questions": ["Please specify the thread size (e.g., M4, M5, M6, M8, M10, M12, etc.)"], "block_all_other_questions": true, "override_all_other_rules": true, "auto_determine": {"when": "countersink_hole_detected && thread_size_missing", "action": "force_thread_size_only", "logic": "Force only thread size question, completely ignore all other missing parameters until thread size is provided"}}, {"id": "CS_017", "category": "workflow_enforcement", "title": "<PERSON><PERSON><PERSON> Required First", "description": "Thread size must be specified first when creating a countersink hole", "rule": "When user requests a countersink hole, thread size must be provided first", "severity": "error", "validation_code": "thread_size == null || thread_size == ''", "error_message": "Missing parameters: thread size", "parameters": ["thread_size"], "priority": "highest", "workflow_step": 1, "questions": ["Please specify the thread size (e.g., M4, M5, M6, M8, M10, M12, etc.)"], "auto_determine": {"when": "countersink_hole_requested && thread_size_missing", "action": "prompt_for_thread_size", "logic": "Ask user to specify thread size first before any other parameters"}}, {"id": "CS_018", "category": "workflow_enforcement", "title": "Hole Type Required After Thread Size", "description": "Hole type (blind or through) must be specified after thread size", "rule": "After thread size is provided, hole type must be specified", "severity": "error", "validation_code": "thread_size != null && thread_size != '' && (hole_type == null || hole_type == '')", "error_message": "Missing parameters: hole type", "parameters": ["thread_size", "hole_type"], "priority": "highest", "workflow_step": 2, "questions": ["Please choose the hole type: Blind or Through"], "auto_determine": {"when": "thread_size_provided && hole_type_missing", "action": "prompt_for_hole_type", "logic": "Ask user to choose hole type after thread size is provided"}}, {"id": "CS_019", "category": "workflow_enforcement", "title": "Hole Depth Required for Blind Holes", "description": "For blind holes, hole depth must be specified", "rule": "When hole type is blind, hole depth must be provided", "severity": "error", "validation_code": "hole_type == 'blind' && (hole_depth == null || hole_depth <= 0)", "error_message": "Missing parameters: hole depth", "parameters": ["hole_type", "hole_depth"], "priority": "highest", "workflow_step": 3, "questions": ["Please specify the depth of the threaded hole"], "auto_determine": {"when": "hole_type == 'blind' && hole_depth_missing", "action": "prompt_for_hole_depth", "logic": "Ask user to specify hole depth when blind hole is selected"}}, {"id": "CS_020", "category": "workflow_enforcement", "title": "Blind Hole Depth Validation", "description": "For blind holes, total depth must not exceed material thickness", "rule": "For blind holes: countersink_depth + hole_depth ≤ material_thickness", "severity": "error", "validation_code": "hole_type == 'blind' && (countersink_depth + hole_depth) > material_thickness", "error_message": "The total depth of the countersink and the threaded hole exceeds the material thickness. This would result in a through hole, not a blind hole.", "parameters": ["hole_type", "countersink_depth", "hole_depth", "material_thickness"], "priority": "highest", "workflow_step": 4, "questions": ["Please reduce the hole depth or increase material thickness"], "auto_determine": {"when": "hole_type == 'blind' && depth_validation_fails", "action": "warn_depth_exceeds_thickness", "logic": "Warn user that total depth exceeds material thickness"}}, {"id": "CS_016", "category": "auto_completion", "title": "Auto-complete Missing Standard Parameters", "description": "When thread size is provided, auto-complete all missing standard parameters", "rule": "Complete design with standard values when thread size is known", "severity": "info", "validation_code": "true", "error_message": "INFO: Design completed with standard parameters.", "parameters": ["thread_size"], "priority": "high", "auto_determine": {"when": "thread_size is provided and workflow_complete", "action": "complete_design", "logic": "Apply all standard dimensions and proceed with CAD generation only after workflow steps are complete"}}, {"id": "CS_007", "category": "standard_compliance", "title": "Countersunk Screw Hole Diameter (d1)", "description": "Hole diameter d1 must match standard countersunk screw specifications", "rule": "Hole diameter d1 must correspond to thread size standard values", "severity": "warning", "validation_code": "(thread_size == 'M4' && d1 == 4.5) || (thread_size == 'M5' && d1 == 5.5) || (thread_size == 'M6' && d1 == 6.6) || (thread_size == 'M8' && d1 == 9) || (thread_size == 'M10' && d1 == 11) || (thread_size == 'M12' && d1 == 13.5) || (thread_size == 'M14' && d1 == 15.5) || (thread_size == 'M16' && d1 == 17.5) || (thread_size == 'M18' && d1 == 20) || (thread_size == 'M20' && d1 == 22)", "error_message": "WARNING: Hole diameter d1 auto-corrected to match thread size standard.", "parameters": ["thread_size", "d1"]}, {"id": "CS_008", "category": "standard_compliance", "title": "Countersunk Screw Countersink Diameter (d2)", "description": "Countersink diameter d2 must match standard countersunk screw specifications", "rule": "Countersink diameter d2 must correspond to thread size standard values", "severity": "warning", "validation_code": "(thread_size == 'M4' && d2 == 8.6) || (thread_size == 'M5' && d2 == 10.4) || (thread_size == 'M6' && d2 == 12.4) || (thread_size == 'M8' && d2 == 16.4) || (thread_size == 'M10' && d2 == 20.4) || (thread_size == 'M12' && d2 == 24.4) || (thread_size == 'M14' && d2 == 27.4) || (thread_size == 'M16' && d2 == 32.4) || (thread_size == 'M18' && d2 == 36.4) || (thread_size == 'M20' && d2 == 40.4)", "error_message": "WARNING: Countersink diameter d2 auto-corrected to match thread size standard.", "parameters": ["thread_size", "d2"]}, {"id": "CS_009", "category": "standard_compliance", "title": "Countersunk Screw Total Depth", "description": "Total depth must match standard countersunk screw specifications", "rule": "Total depth must correspond to thread size standard values", "severity": "warning", "validation_code": "(thread_size == 'M4' && total_depth == 2.1) || (thread_size == 'M5' && total_depth == 2.5) || (thread_size == 'M6' && total_depth == 2.9) || (thread_size == 'M8' && total_depth == 3.7) || (thread_size == 'M10' && total_depth == 4.7) || (thread_size == 'M12' && total_depth == 5.2) || (thread_size == 'M14' && total_depth == 5.7) || (thread_size == 'M16' && total_depth == 7.2) || (thread_size == 'M18' && total_depth == 8.2) || (thread_size == 'M20' && total_depth == 9.2)", "error_message": "WARNING: Countersink depth auto-corrected to match thread size standard.", "parameters": ["thread_size", "total_depth"]}, {"id": "CS_010", "category": "standard_compliance", "title": "Head <PERSON>rew Hole Diameter (d1)", "description": "Hole diameter d1 must match standard head screw specifications", "rule": "For head screws, hole diameter d1 must correspond to thread size standard values", "severity": "error", "validation_code": "screw_head_type != 'head' || ((thread_size == 'M4' && d1 == 4.5) || (thread_size == 'M5' && d1 == 5.5) || (thread_size == 'M6' && d1 == 6.6) || (thread_size == 'M8' && d1 == 9) || (thread_size == 'M10' && d1 == 11) || (thread_size == 'M12' && d1 == 13.5))", "error_message": "ERROR: Head screw hole diameter d1 does not match standard for selected thread size.", "parameters": ["thread_size", "d1", "screw_head_type"]}, {"id": "CS_011", "category": "standard_compliance", "title": "Head <PERSON><PERSON>sink Diameter (d2)", "description": "Countersink diameter d2 must match standard head screw specifications", "rule": "For head screws, countersink diameter d2 must correspond to thread size standard values", "severity": "error", "validation_code": "screw_head_type != 'head' || ((thread_size == 'M4' && d2 == 8) || (thread_size == 'M5' && d2 == 10) || (thread_size == 'M6' && d2 == 11) || (thread_size == 'M8' && d2 == 15) || (thread_size == 'M10' && d2 == 18) || (thread_size == 'M12' && d2 == 20))", "error_message": "ERROR: Head screw countersink diameter d2 does not match standard for selected thread size.", "parameters": ["thread_size", "d2", "screw_head_type"]}, {"id": "CS_012", "category": "standard_compliance", "title": "Head <PERSON>rew Total Depth", "description": "Total depth must match standard head screw specifications", "rule": "For head screws, total depth must correspond to thread size standard values", "severity": "error", "validation_code": "screw_head_type != 'head' || ((thread_size == 'M4' && total_depth == 8) || (thread_size == 'M5' && total_depth == 10) || (thread_size == 'M6' && total_depth == 11) || (thread_size == 'M8' && total_depth == 15) || (thread_size == 'M10' && total_depth == 18) || (thread_size == 'M12' && total_depth == 20))", "error_message": "ERROR: Head screw total depth does not match standard for selected thread size.", "parameters": ["thread_size", "total_depth", "screw_head_type"]}]}