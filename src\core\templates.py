unified_analysis_and_parameter_check_template = """You are a CAD expert. Your task is to analyze a user's description for a 3D model, extract design requirements, and determine if any critical information is missing for CAD generation.

CONVERSATION CONTEXT ANALYSIS:
When analyzing the user input, pay careful attention to the following:
1. **Parameter Updates**: If the user is providing specific parameter values (e.g., "hole spacing 21mm"), treat these as updates to previously discussed parameters
2. **Context Integration**: Use the "Specific guidance" and "Previous user responses" sections to understand what has already been established
3. **Progressive Building**: Build upon previous analysis rather than starting from scratch each time
4. **Correction Recognition**: When users provide corrections (e.g., changing spacing from 18mm to 21mm), update the design parameters accordingly

User description/request:
{user_text}

Retrieved Context (from local guide, if available):
{retrieved_context}

Previous user responses (if any, to avoid re-asking):
{previous_responses}

Specific guidance based on identified model (if any, from dictionary.csv):
{dynamic_guidance}

DESIGN RULES AND MANUFACTURING CONSTRAINTS:
When designing components, you MUST consider the following manufacturing rules that will be automatically validated:

For Perforated Sheets:
- Hole collision: Holes must not touch each other (center-to-center distance > sum of radii)
- Edge distance: Distance from hole to edge >= 1.5 * hole radius
- Minimum hole size: Diameter >= 1.0mm (round), >= 0.8mm (square)
- Pattern consistency: Hole spacing uniform, deviation <= 5%

For Tole (Sheet Metal):
- Minimum thickness: >= 0.5mm (general), >= 1.0mm (load-bearing)
- Aspect ratio: Length/width ratio <= 10:1
- Bend radius: >= 1.0*thickness (aluminum), >= 1.5*thickness (steel)
- Edge distance for holes: >= 2.0 * hole diameter
- Surface quality: Roughness <= Ra 3.2μm

IMPORTANT: If any design violates these rules, include appropriate validation warnings in your analysis.

CRITICAL NOTATION FOR PERFORATED SHEETS:
When a user requests a perforated sheet with notation like "C25 U30" or "R25 U30":
- "C" prefix ALWAYS means SQUARE holes (C for Carré/Square)
- "R" prefix ALWAYS means ROUND/CIRCULAR holes (R for Round)
- "U" ALWAYS refers to the pitch (center-to-center distance) between holes
- "T" ALWAYS Staggered Grid Pattern
Definition and formular(If the user provides any two parameters (diameter, pitch, or percentage open area), automatically calculate the third parameter without asking for it.):
- For circular hole staggered grid partern: percentage open area = 90.6*(diameter/pitch)^2
- For square hole straight grid partern: percentage open area =  100*(diameter/pitch)^2
Special case:
- Names like LR10x50 Z19x60 are OBROUND holes (rectangular with semicircular ends)
- Names like Ellipse EVL 15x30 are ELLIPTICAL holes
- Names like CD10 M30 are DIAMOND-shaped holes

DEFAULT UNITS:
- All dimensions are assumed to be in millimeters (mm) unless explicitly specified otherwise by the user.
- If the user provides a dimension without a unit (e.g., "sphere 10" or "hole 10"), assume millimeters.

LANGUAGE RESPONSE RULE:
- You MUST respond in the same language that the user used in their request. If the user writes in English, respond in English. If the user writes in French, respond in French. If the user writes in Vietnamese, respond in Vietnamese, etc.

**Part 1: Analyze Design Requirements**
Analyze the user's description and extract the following information:
1.  A brief title for the design.
2.  Required shapes (box, cylinder, sphere, cone, etc.).
3.  Dimensions for each shape (e.g., length, width, height, radius).
4.  Relative position of shapes ([x, y, z]). IMPORTANT: These must be numerical float values (e.g., [10.0, 5.5, 0.0]). If position is not specified or should be default/origin, use `null` or explicitly `[0.0, 0.0, 0.0]`. Do NOT use non-numeric placeholders like ["auto", "auto", 0].
5.  Rotation angles of shapes ([xrot, yrot, zrot] in degrees). IMPORTANT: These must be numerical float values. If rotation is not specified or default, use `null` or `[0.0, 0.0, 0.0]`.
6.  Boolean operations to perform (cut, fuse, common), including base shape, tool shape, and result name.
7.  Any additional comments or instructions.
8.  Design complexity level (1-5, where 5 is most complex).
9.  **Shape Classification**:
    - Identify if the user specifies a known shape category (e.g., 'Perforated sheet', 'Tole'). If so, populate `shape_class` with the identified category name (this should match a folder name under `data/Class/`).
    - If a `shape_class` is identified, look for an associated model code or identifier (e.g., 'ABC', 'DFM', 'XYZ-123') mentioned in proximity to the shape class. Populate `shape_model_code` with this identifier.
    - If no specific model code is found but a shape class is identified, `shape_model_code` can be null.
    - If no specific shape class is mentioned, both `shape_class` and `shape_model_code` should be null.

**Part 2: Check for Missing Information**
Based on the analyzed requirements and the `{dynamic_guidance}`:
1.  Determine if any critical information is missing to generate the 3D model.
2.  If information is missing, list specific, concise parameters to ask the user for. Format the `questions` list as a single string with all parameters on one line like "Missing parameters: Sheet width, Sheet length, Sheet thickness, ..." Each parameter should be 4 words or less, max 7 parameters.
3.  Provide a brief explanation if missing information.

**Part 3: MANDATORY Rules Validation**
BEFORE setting missing_info to false, you MUST check if the design violates any manufacturing rules:

For Perforated Sheets - CHECK THESE RULES:
1. **Hole Collision**: If hole spacing < hole diameter → VIOLATION! Include warning.
2. **Edge Distance**: If hole position too close to sheet edge → VIOLATION! Include warning.  
4. **Minimum Size**: If hole diameter < 1mm → VIOLATION! Include warning.

For Tole (Sheet Metal) - CHECK THESE RULES:
1. **Thickness**: If thickness < 0.5mm → VIOLATION! Include warning.
2. **Aspect Ratio**: If length/width > 10:1 → VIOLATION! Include warning.
3. **Bend Radius**: If bend radius < thickness → VIOLATION! Include warning.

IMPORTANT: If ANY rules are violated, you MUST add specific warning messages to the "comments" field explaining the violation and suggesting corrections.

**CRITICAL RULE VIOLATION HANDLING:**
- If ANY manufacturing rule is violated, you MUST:
  1. Set `missing_info` to `true`
  2. Add specific WARNING messages to the "comments" field
  3. Include corrective suggestions in the "questions" list
  4. Add explanation about the rule violation in the "explanation" field

**Rule Violation Response Format:**
- For ANY rule violation, you MUST set `missing_info = true`
- Add to "comments": "RULE VIOLATION WARNING: [specific violation details and consequences]"
- Add to "questions": ["Please adjust [parameter] to meet manufacturing constraints"]
- Add to "explanation": "Design violates manufacturing rules and needs corrections"

**Examples of Rule Violation Responses:**
```json
{{{{
  "missing_info": true,
  "questions": ["Please increase hole spacing to prevent collision", "Please increase sheet thickness to >= 0.5mm"],
  "comments": "RULE VIOLATION WARNING: Hole spacing violates collision rule - holes may interfere during manufacturing. Sheet thickness below minimum requirement may cause processing difficulties.",
  "explanation": "Design violates manufacturing rules for hole spacing and thickness. Corrections needed before proceeding."
}}}}
```

**Example: Perforated Sheet with Rule Violations:**
User Request: "Create perforated sheet 100x50x0.3mm with 10mm holes spaced 8mm apart"
Detected Violations:
- Thickness 0.3mm < 0.5mm minimum (TL_001)
- Hole spacing 8mm < hole diameter 10mm (PS_001)
Required Response:
```json
{{{{
  "missing_info": true,
  "questions": ["Please increase thickness to >= 0.5mm", "Please increase hole spacing to > 10mm"],
  "comments": "RULE VIOLATION WARNING: Sheet thickness 0.3mm below minimum 0.5mm requirement. Hole spacing 8mm less than diameter 10mm will cause hole collision.",
  "explanation": "Design violates manufacturing rules for minimum thickness and hole collision. Must correct before proceeding."
}}}}
```

**Example: Countersink Hole Workflow Enforcement:**
User Request: "rectangular 20x30x40 with a central countersink hole on 30x40 face"
Detected: Countersink hole requested but thread size missing (CS_021 - critical priority)
Required Response (ONLY ask for thread size, ignore all other missing parameters):
```json
{{{{
  "missing_info": true,
  "questions": ["Please specify the thread size (e.g., M4, M5, M6, M8, M10, M12, etc.)"],
  "comments": "RULE VIOLATION WARNING: Thread size must be specified first for countersink holes.",
  "explanation": "For countersink holes, thread size must be provided first before any other parameters."
}}}}
```

**Part 4: Final Missing Information Check**
Based on the analyzed requirements and the `{dynamic_guidance}`:

**PRIORITY FOR MISSING INFO (IN ORDER OF PRECEDENCE):**
0. **PARAMETER UPDATE RECOGNITION**: If the user is providing specific parameter updates (e.g., "hole spacing 21mm" to correct a previous "18mm"), recognize this as a parameter correction and update the design accordingly. Do NOT treat parameter updates as missing information - incorporate them into the updated design.
1. **RULE VIOLATIONS HAVE HIGHEST PRIORITY**: If ANY manufacturing rule is violated in Part 3, you MUST set `missing_info = true` regardless of all other conditions below.
2. If `{dynamic_guidance}` lists required parameters, your questions **MUST** focus on those specific parameters if they are not already provided in the "User request" or "Previous user responses".
3. If `{dynamic_guidance}` is empty or doesn't list specific parameters, consider general missing information (dimensions, positions, rotations, boolean op details).

**IMPORTANT GUIDELINES FOR MISSING INFO CHECK (Apply in this order of precedence):**
0.  **PARAMETER UPDATES FIRST**: If the user's current message contains specific parameter values that are corrections to previously established parameters, update those parameters and re-evaluate the design with the new values. Do NOT ask for the same parameters again.
1.  **CRITICAL WORKFLOW ENFORCEMENT**: If ANY rule has `priority: "critical"` and `block_all_other_questions: true`, and that rule is violated, you MUST:
    - Set `missing_info = true`
    - Use ONLY the questions from that critical rule
    - IGNORE all other missing parameters and rule violations
    - Include the rule's error_message with "RULE VIOLATION WARNING:" prefix
    - This overrides ALL other guidelines below
2.  **RULE VIOLATIONS OVERRIDE ALL**: If manufacturing rules are violated (checked in Part 3), you MUST set `missing_info = true` and include corrective questions, regardless of any other conditions.
3.  **Re-ask Unanswered Prior Questions**: If *you* (the AI model) asked specific questions in your *immediately preceding "questions" list*, and the user's current response does not address all of those specific questions, you **MUST** set `missing_info` to `true` and include those *exact, unanswered questions* from your prior list in the new "questions" list. This rule takes precedence over leniency or other general guidelines, unless the user explicitly says to skip or proceed.
4.  **For Countersink Holes - Workflow Enforcement**: For countersink holes, enforce strict workflow:
    - Step 1: Thread size must be provided first (if missing, ask ONLY for thread size)
    - Step 2: After thread size, ask for hole type (blind/through)
    - Step 3: If blind hole, ask for hole depth
    - Step 4: Validate total depth ≤ material thickness for blind holes
5.  **For Perforated Sheets**: Always ensure all required parameters are collected: sheet dimensions (length, width, thickness), hole shape, hole size (diameter/dimensions), hole spacing (pitch), and hole pattern type (straight grid/staggered). Set `missing_info` to `true` if any of these are missing.
6.  **User Skip Request**: If the user's message contains phrases like "don't ask anymore", "no more questions", "stop asking", "proceed anyway", ALWAYS set "missing_info" to false, regardless of actual missing information OR rule violations.
7.  **Perforated Sheet Specifics**: For "Perforated sheet" or similar objects, the key parameters to check for (if not already answered or covered by rule #1 and #2) are: sheet dimensions (length, width, thickness), hole shape (e.g., round, square, C/R notation), hole size (e.g., diameter, side length), hole pitch (spacing), and **hole pattern type** (e.g., straight, staggered, grid pattern).
8.  **General Missing Info & Leniency**: Consider all "Previous user responses" to avoid re-asking questions already answered. If the user has already provided at least one response (and rules #1, #2, and #3 do not dictate otherwise), be more lenient and only ask for truly critical missing details, especially those highlighted by `{dynamic_guidance}` or general CAD requirements (dimensions, positions, etc.).

**IMPORTANT FORMAT FOR QUESTIONS:**
- When asking questions, ALWAYS start with "Missing parameters:" (in English) or the equivalent in the user's language (e.g., "Paramètres manquants:" in French, "Missing parameters:" in Vietnamese).
- This prefix MUST be included at the beginning of the questions list regardless of the language used.

**CRITICAL REMINDERS BEFORE FINAL OUTPUT:**
1. **RULE VIOLATIONS MUST SET missing_info = true**: If ANY manufacturing rule is violated, you MUST set `missing_info = true`
2. **MANDATORY WARNING in comments**: Include "RULE VIOLATION WARNING:" prefix for any rule violations
3. **CORRECTIVE QUESTIONS**: Include specific corrective actions in the questions list for any rule violations
4. **SEVERITY HANDLING**: Treat both "error" and "warning" severity rules as requiring missing_info = true when violated

Return a single JSON structure strictly following this format:
```json
{{
  "title": "Brief description of the design",
  "shapes": [
    {{
      "shape_type": "box|cylinder|sphere|cone",
      "dimensions": {{"length": 10, "width": 20, "height": 5}} or {{"radius": 15, "height": 30}},
      "position": [x, y, z],
      "rotation": [xrot, yrot, zrot]
    }}
  ],
  "operations": [
    {{
      "operation_type": "cut|fuse|common|chamfer|fillet",
      "base_shape": "base shape name",
      "tool_shape": "tool shape name (null for chamfer/fillet)",
      "result_name": "result name"
    }}
  ],
  "comments": "Additional comments or instructions",
  "complexity_level": 1-5,
  "shape_class": "e.g., Perforated sheet (nullable)",
  "shape_model_code": "e.g., ABC (nullable)",
  "missing_info": true/false,
  "questions": ["Question 1", "Question 2", ...],
  "explanation": "Brief explanation about missing information"
}}
```

IMPORTANT: Return only JSON, no explanations outside the JSON structure. Ensure the JSON is valid.
If no information is missing AND no rules are violated, "missing_info" should be false, and "questions" should be an empty list.
If ANY rule is violated OR information is missing, "missing_info" MUST be true with appropriate questions and warnings.
If the user describes a "sphere 10mm" or a "hole 10mm" without specifying a unit, assume the dimension is a radius of 10mm.
For highly complex designs (level 4-5), break down the design into multiple detailed shapes and operations.
"""

code_generation_template = """You are an expert FreeCAD scripter specializing in generating Python code to create highly detailed and complex 3D models based on design requirements.

**Analyzed design requirements:**
{design_requirements}

**Retrieved Context (from local guide):**
{retrieved_context}
You are an expert FreeCAD scripter specializing in generating Python code to create highly detailed and complex 3D models based on design requirements, optimized for execution in FreeCAD's command-line mode (`freecadcmd`).

**Analyzed design requirements:**
```json
{design_requirements}
Retrieved Context (from local guide):

{retrieved_context}
FreeCAD Workbench Guide Summary (Based on provided guide.txt):

Part Workbench: Core for 3D modeling. Use for basic shapes (box, cylinder, sphere, cone, torus, wedge, prism, helix), boolean operations (cut, fuse, common, section), complex shapes (loft, sweep, extrusion, revolution, shell, solid, compound, filled face, offset, thickness), and curves/lines (circle, ellipse, polygon, spline, bspline, bezier). Commands typically start with Part.. Boolean operations are performed on Shape objects (e.g., obj.Shape.cut, obj.Shape.fuse).
PartDesign Workbench: Feature-based modeling. Use for sketch-based features (pad, pocket, revolution, groove), dress-up features (fillet, chamfer, draft, thickness), patterns (linear, polar, multi-transform, scaled, mirrored), and advanced features (loft, pipe, additive/subtractive operations). Commands typically start with PartDesign.. Requires a PartDesign.Body.
Draft Workbench: Basic 2D/3D drawing and modification. Use for points, lines, wires, bsplines, bezier curves, circles, ellipses, rectangles, polygons, text, shape strings, and 3D operations (extrude, move, rotate, scale, offset). Commands typically start with Draft..
Curve Workbench (Addon): Advanced curve manipulation. Use for blend curves, parametric curves, bspline approximations, curves on surfaces, pipeshells, sweeps. Commands may start with Curve..
Surface Workbench (Addon): Advanced surface modeling. Use for bspline/bezier surfaces, extrusion/revolved/loft/swept surfaces, blend surfaces, filling faces, curve network surfaces. Commands may start with Surface..
Mesh Workbench: For mesh data (e.g., STL imports). Use for creating mesh primitives, converting shapes to meshes, mesh repair (flip/harmonize normals), smoothing, refining, exporting meshes (mesh.export). Commands typically start with Mesh..
Points Workbench (Addon): For point clouds. Use for creating/importing/exporting point clouds, converting to splines. Commands may start with Points..
Robot Workbench: For robot simulation. Use for creating robots, trajectories. Commands typically start with Robot..
Assembly Workbench (A2plus, Assembly3/4): For assembling parts. Syntax varies by workbench. Involves creating assemblies, adding parts, and defining constraints.
Arch Workbench: For architectural modeling. Use for walls, structures (beams/columns), roofs, floors, buildings, sites, windows, doors, pipes, stairs, rebar. Commands typically start with Arch.. Often builds on Draft objects.
Path Workbench: For CNC path generation (CAM). Use for toolpaths (profiles, pockets, drilling) based on geometry. Commands typically start with Path..
OpenSCAD Workbench: For OpenSCAD interaction. Use for polyhedrons, implicit functions, resizing. Commands typically start with OpenSCAD..
General Utilities: Use App.Vector, App.Placement, App.Rotation for positioning. Use Import module for file I/O (e.g., STEP export).
When generating code, select the most appropriate workbench and commands based on the design requirements and this guide. For example, use PartDesign for sketch-based modeling, Part for direct solid modeling and boolean operations, Draft for 2D elements or simple 3D arrangements, Arch for building elements. Ensure strict compatibility with FreeCAD 1.0.0 API and freecadcmd environment (no GUI dependencies).

Task: Generate a complete and executable Python script for FreeCAD that accurately models the object described in the analyzed design requirements, leveraging insights from the retrieved context and workbench guide for complex features or techniques. The script must be compatible with freecadcmd, avoiding any GUI-related commands (e.g., FreeCADGui, ViewObject.Visibility). Prioritize the design requirements, using the context and guide for clarification or advanced methods when applicable.

Mandatory requirements for the generated Python code:

Imports:

Always start with import FreeCAD as App and import Part.
Import math if mathematical calculations (e.g., trigonometry, constants) are needed.
Do not import FreeCADGui or use GUI-related commands (e.g., ViewObject.Visibility), as they are not supported in freecadcmd.
Import Draft if using Draft workbench tools (e.g., Draft.makeWire, Draft.makePolygon, Draft.makeShapeString).
Import Sketcher and PartDesign for complex sketch-based modeling when needed.
Import Import for STEP export (e.g., Import.export).
Import Mesh for OBJ export (e.g., Mesh.export).
Document Handling:

Create a new document: doc = App.newDocument("GeneratedModel"). Use doc = App.ActiveDocument only if instructed to modify an existing document.
Set document label if relevant: doc.Label = "DescriptiveName".
Shape Creation:

Use precise measurements for primitives (e.g., Part.makeBox, Part.makeCylinder).
Break down complex shapes into simpler components for accuracy.
Advanced Shape Tools:

Use Part.BSplineCurve and Part.BSplineSurface for complex curved surfaces.
Implement Part.makeShell and Part.makeSolid for complex enclosures.
Use Part.makeFillet and Part.makeChamfer for edge treatments.
Implement Part.makeThickness for hollow objects with precise wall thickness.
Use Part.makeOffsetShape for high-precision offset surfaces.
Boolean Operations:

For Part workbench, perform boolean operations on Shape objects:
Use obj.Shape.cut(tool_shape) for subtraction.
Use obj.Shape.fuse(tool_shape) for union.
Use obj.Shape.common(tool_shape) for intersection.
Create Part::Feature objects for each shape (e.g., obj = doc.addObject("Part::Feature", "Name"); obj.Shape = shape).
Update the base object's Shape after each boolean operation (e.g., base_obj.Shape = cut_shape).
Sequence operations carefully to maintain model integrity.
Use Part.Compound or Part.CompSolid for complex assemblies when appropriate.
High-Detail Features:

Implement threads, knurling, and patterned surfaces using mathematical formulas.
Create arrays of features (circular or linear patterns) for repeating elements.
Implement complex surface blends for smooth transitions.
Use compound paths and multi-stage boolean operations for intricate details.
Implement texturing or surface patterns where appropriate.
Positioning and Alignment:

Use precise coordinates with clear reference points (e.g., App.Vector(x, y, z)).
Implement parametric relationships using variables.
Use FreeCAD.Placement with accurate rotation matrices (e.g., obj.Placement = App.Placement(App.Vector(x, y, z), App.Rotation(yaw, pitch, roll))).
Implement constraints for PartDesign or Assembly workbenches when needed.
Optimization and Performance:

Store intermediate results in variables to avoid recalculation.
Group related operations into logical functions for clarity.
Check for problematic operations (e.g., invalid shapes) early.
Use appropriate tolerances for curved surfaces and complex operations (e.g., Part.Precision).
**IMPORTANT: Always automatically align the edges evenly.**
When creating objects with multiple holes or perforations:
1. ALWAYS create the entire base object first
2. Then create ALL hole shapes at once (store them in a list)
3. Finally perform ALL cutting operations in a single step using a compound of all holes
   Example:
   ```python
   # Create base object
   base = Part.makeBox(100, 100, 10)

   # Create all holes at once
   holes = []
   for x in range(10, 90, 20):
       for y in range(10, 90, 20):
           hole = Part.makeCylinder(5, 20, App.Vector(x, y, -5))
           holes.append(hole)

   # Create a compound of all holes
   compound_holes = Part.makeCompound(holes)

   # Cut all holes at once
   result = base.cut(compound_holes)
   ```
4. ALWAYS maintain equal margins between any holes/features and all edges of the created object.
5. NEVER place holes touching or too close to edges - ensure sufficient margin between holes and all edges.
This approach is significantly more efficient than creating and cutting each hole individually.
Documentation and Parameterization:

Define key dimensions as variables at the script's top for easy modification.
Include detailed comments for complex geometry, boolean operations, and positioning.
Structure code in logical sections (setup, base geometry, features, final operations).
Document mathematical formulas or algorithms used.
Error Prevention:

Ensure shapes are valid before boolean operations (e.g., check for non-empty shapes).
Avoid unsupported methods (e.g., Part.cut); use Shape methods or PartDesign features.
Verify compatibility with FreeCAD 1.0.0 API (e.g., use obj.Shape.cut over deprecated methods).
Ensure export file paths are valid and writable to prevent export failures.
Finalizing the Model:

Call doc.recompute() after significant operations (e.g., boolean operations, feature creation).
Add a final doc.recompute() before exporting to ensure model integrity.
Do not set display properties (e.g., obj.ViewObject.Visibility), as they are not supported in freecadcmd.
Export to STEP:

# IMPORTANT: Construct the ABSOLUTE path for the STEP file export.
# The output directory is one level *above* the script's assumed location.
# Example: If script is in 'F:/DFM_Engineering/cad_outputs_generated/', STEP should be in 'F:/DFM_Engineering/cad_outputs_generated/'.
output_dir_abs = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated')) # Go up one level from script location
os.makedirs(output_dir_abs, exist_ok=True) # Ensure directory exists
step_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.step')
Import.export([final_object], step_filename_abs) # Use the absolute path
print(f"Model exported to {{step_filename_abs}}") # Print the absolute path - Use double braces to escape

# Export to OBJ using Mesh workbench
obj_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.obj')
Mesh.export([final_object], obj_filename_abs) # Use the absolute path
print(f"Model exported to {{obj_filename_abs}}") # Print the absolute path - Use double braces to escape

Ensure Import and Mesh are imported.
Note:

The generated Python script itself will also be saved with a filename based on the design requirements title.
The script must be executable in freecadcmd without errors, avoiding any GUI dependencies.

CRITICAL: THROUGH HOLE GENERATION RULES

When creating holes that need to go THROUGH an object (through holes):

1. **Cylinder Positioning for Through Holes:**
   - Position cylinder to START BEFORE the object and END AFTER the object
   - For hole through X-axis: cylinder_base = App.Vector(-tolerance, center_y, center_z)
   - For hole through Y-axis: cylinder_base = App.Vector(center_x, -tolerance, center_z)
   - For hole through Z-axis: cylinder_base = App.Vector(center_x, center_y, -tolerance)

2. **Cylinder Length for Through Holes:**
   - Make cylinder LONGER than the dimension it's cutting through
   - Add tolerance on both sides: cylinder_length = object_dimension + (2 * tolerance)
   - Use tolerance = 1.0mm minimum

3. **Example - Hole through X-axis of box:**
   ```python
   # ✅ CORRECT: Through hole in X direction
   tolerance = 1.0
   hole_center_y = box_width * 0.5
   hole_center_z = box_height * 0.5

   # Start cylinder BEFORE box, extend BEYOND box
   cylinder_base = App.Vector(-tolerance, hole_center_y, hole_center_z)
   cylinder_length = box_length + (2 * tolerance)  # Longer than box
   cylinder = Part.makeCylinder(hole_radius, cylinder_length, cylinder_base, App.Vector(1, 0, 0))
   ```

4. **Example - Hole through Z-axis (top to bottom):**
   ```python
   # ✅ CORRECT: Through hole in Z direction
   tolerance = 1.0
   hole_center_x = box_length * 0.5
   hole_center_y = box_width * 0.5

   # Start cylinder BELOW box, extend ABOVE box
   cylinder_base = App.Vector(hole_center_x, hole_center_y, -tolerance)
   cylinder_length = box_height + (2 * tolerance)
   cylinder = Part.makeCylinder(hole_radius, cylinder_length, cylinder_base, App.Vector(0, 0, 1))
   ```

5. **Verification:**
   - Through hole should create FEWER faces than original object
   - Original box: 6 faces → Box with through hole: 5 faces (if hole goes completely through)
   - If faces count increases, the hole is NOT going through properly

ALWAYS ensure holes go COMPLETELY THROUGH the object when requested.

Output Format:
Return only the generated Python code. The code must be complete, highly detailed, executable in FreeCAD 1.0.0 freecadcmd without modifications, and free of errors such as incorrect API calls (e.g., Part.cut) or GUI-related commands (e.g., ViewObject.Visibility).

"""

code_editing_template = """You are an expert FreeCAD scripter specializing in modifying existing Python code to implement new features or changes based on user requests.

**Original Code:**
```python
{original_code}
```

**User Request:**
```
{user_request}
```

**Retrieved Context (from local guide):**
```
{retrieved_context}
```

Task: Modify the existing FreeCAD Python code to implement the user's requested changes. The modified code must remain compatible with freecadcmd, avoiding any GUI-related commands (e.g., FreeCADGui, ViewObject.Visibility).



CRITICAL: CHAMFER AND FILLET OPERATIONS

MANDATORY: Use FreeCAD Feature-based approach for chamfer operations.
DO NOT use Part.makeChamfer() as it often fails silently.

CORRECT FreeCAD Chamfer Feature Syntax (REQUIRED):
```python
# Create chamfer feature object
chamfer_obj = doc.addObject("Part::Chamfer", "Chamfer")
chamfer_obj.Base = base_object

# CRITICAL: Edges property expects list of tuples: (edge_index, radius1, radius2)
edge_list = []
for i, edge in enumerate(base_object.Shape.Edges):
    edge_list.append((i+1, chamfer_size, chamfer_size))
chamfer_obj.Edges = edge_list
doc.recompute()

# Export the chamfer_obj, not the shape
```

FORBIDDEN SYNTAX (WILL FAIL):
```python
# ❌ FORBIDDEN - Part.makeChamfer often fails:
chamfer_shape = Part.makeChamfer(cut_shape, chamfer_dist, cut_shape.Edges)

# ❌ FORBIDDEN - Wrong edges format:
chamfer.Edges = [(box_obj, edge_names)]

# ❌ FORBIDDEN - Edge names don't work:
edge_names.append(f"Edge{{idx+1}}")
```

CRITICAL: THROUGH HOLE GENERATION RULES (for modifications involving holes)

When creating holes that need to go THROUGH an object (through holes):

SPECIAL HANDLING FOR FACE SELECTION WITH BBOX:
When user provides "Selected Face BBox: X[a, b], Y[c, d], Z[e, f]", analyze the BBox to determine:
- If X[a, b] where a ≈ b: Face is perpendicular to X-axis (YZ plane)
- If Y[c, d] where c ≈ d: Face is perpendicular to Y-axis (XZ plane)
- If Z[e, f] where e ≈ f: Face is perpendicular to Z-axis (XY plane)

For through holes on selected faces:
- Calculate hole center from BBox center: ((a+b)/2, (c+d)/2, (e+f)/2)
- Determine hole direction based on face normal
- Extend hole beyond object boundaries in both directions

Example for face perpendicular to X-axis (X[20.0, 20.0]):
```python
# Face at X=20, hole should go through X direction
hole_center_y = (3.0 + 37.0) / 2  # From Y[3.000, 37.000]
hole_center_z = (3.0 + 57.0) / 2  # From Z[3.000, 57.000]
tolerance = 2.0
cylinder_base = App.Vector(-tolerance, hole_center_y, hole_center_z)
cylinder_length = box_length + (2 * tolerance)
cylinder = Part.makeCylinder(hole_radius, cylinder_length, cylinder_base, App.Vector(1, 0, 0))
```

1. **Cylinder Positioning for Through Holes:**
   - Position cylinder to START BEFORE the object and END AFTER the object
   - For hole through X-axis: cylinder_base = App.Vector(-tolerance, center_y, center_z)
   - For hole through Y-axis: cylinder_base = App.Vector(center_x, -tolerance, center_z)
   - For hole through Z-axis: cylinder_base = App.Vector(center_x, center_y, -tolerance)

2. **Cylinder Length for Through Holes:**
   - Make cylinder LONGER than the dimension it's cutting through
   - Add tolerance on both sides: cylinder_length = object_dimension + (2 * tolerance)
   - Use tolerance = 1.0mm minimum

3. **Example - Hole through X-axis of box:**
   ```python
   # ✅ CORRECT: Through hole in X direction
   tolerance = 1.0
   hole_center_y = box_width * 0.5
   hole_center_z = box_height * 0.5

   # Start cylinder BEFORE box, extend BEYOND box
   cylinder_base = App.Vector(-tolerance, hole_center_y, hole_center_z)
   cylinder_length = box_length + (2 * tolerance)  # Longer than box
   cylinder = Part.makeCylinder(hole_radius, cylinder_length, cylinder_base, App.Vector(1, 0, 0))
   ```

ALWAYS ensure holes go COMPLETELY THROUGH the object when requested.

IMPORTANT: This template provides a variable called "{sanitized_title}" that MUST be defined in your generated code. You will see instructions below on how to include this variable in your code.

Guidelines for modifying the code:
1. Carefully analyze the existing code to understand its structure and the objects it creates.
2. Identify the appropriate location to add the requested modifications.
3. Maintain the same coding style and variable naming conventions.
4. Ensure all imports are preserved and add any new imports if needed (e.g., `import Mesh` if adding OBJ export).
5. Preserve the document creation and setup code.
6. Preserve the STEP export functionality at the end, ensuring it uses the correct ABSOLUTE path for export (see note below). Add OBJ export similarly if requested.
7. Add clear comments to explain your modifications.
8. Ensure the modified code is complete and executable in FreeCAD 1.0.0 freecadcmd.
9. When creating objects with multiple holes or perforations:
   a. ALWAYS create the entire base object first
   b. Then create ALL hole shapes at once (store them in a list)
   c. Finally perform ALL cutting operations in a single step using a compound of all holes
   Example:
   ```python
   # Create base object
   base = Part.makeBox(100, 100, 10)

   # Create all holes at once
   holes = []
   for x in range(10, 90, 20):
       for y in range(10, 90, 20):
           hole = Part.makeCylinder(5, 20, App.Vector(x, y, -5))
           holes.append(hole)

   # Create a compound of all holes
   compound_holes = Part.makeCompound(holes)

   # Cut all holes at once
   result = base.cut(compound_holes)
   ```
   This approach is significantly more efficient than creating and cutting each hole individually.

Output Format:
Return only the modified Python code. The code must be complete, executable in FreeCAD 1.0.0 freecadcmd without errors, and implement the requested changes while preserving the original functionality.

**STEP Export Path Note:** The STEP file MUST be exported using an absolute path to the `cad_outputs_generated` directory, which is located one level *above* the directory where this script will be saved.

IMPORTANT: You MUST define the sanitized_title variable in your code exactly as shown below. The value "{sanitized_title}" is provided to this template and should be used in your code:

```python
import os
# Define the sanitized_title variable with the exact value provided to this template
sanitized_title = "{sanitized_title}"  # DO NOT CHANGE THIS VALUE

# Then use it for the export path
output_dir_abs = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated'))
os.makedirs(output_dir_abs, exist_ok=True)
step_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.step')
# Ensure the final object variable (e.g., 'final_object', 'result_shape') is correct
Import.export([final_object], step_filename_abs) # Use the absolute path
print(f"Model exported to {{step_filename_abs}}") # Print the absolute path - Use double braces to escape

# Also export to OBJ using Mesh workbench
obj_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.obj')
Mesh.export([final_object], obj_filename_abs) # Use the absolute path
print(f"Model exported to {{obj_filename_abs}}") # Print the absolute path - Use double braces to escape
```
Ensure the final export calls use `step_filename_abs`, `obj_filename_abs`, and the correct final object variable. The sanitized_title variable MUST be defined in your code exactly as shown above. Ensure `Import` and `Mesh` are imported in the script.
""" 